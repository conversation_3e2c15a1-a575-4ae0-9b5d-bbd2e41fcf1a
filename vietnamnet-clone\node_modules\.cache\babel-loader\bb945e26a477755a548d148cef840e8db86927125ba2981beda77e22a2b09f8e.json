{"ast": null, "code": "var _jsxFileName = \"E:\\\\Hoc_Python\\\\FastAPI_Hoc\\\\Giao_Dien_tintuc\\\\vietnamnet-clone\\\\src\\\\components\\\\Header.js\";\nimport React from 'react';\nimport './Header.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = () => {\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"header\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-top\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-hashtags\",\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            className: \"hashtag\",\n            children: \"# \\u0110i\\u1EC3m chu\\u1EA9n \\u0110H-C\\u0110\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-links\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            className: \"header-link\",\n            children: \"Podcast\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            className: \"header-link\",\n            children: \"Tu\\u1EA7n Vi\\u1EC7t Nam\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            className: \"header-link premium\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"https://vnn-res.vgcloud.vn/ResV9/images/vpremium-menu-logo.svg\",\n              alt: \"Premium\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 17,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            className: \"header-link global\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"https://static.vnncdn.net/v1/icon/VietnamNet_bridge.svg\",\n              alt: \"Global\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-main\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-container\",\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/\",\n            className: \"logo-link\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"https://static.vnncdn.net/v1/logo/logoVietnamNet.svg\",\n              alt: \"VietNamNet\",\n              className: \"logo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-container\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"T\\xECm ki\\u1EBFm...\",\n              className: \"search-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"search-button\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://static.vnncdn.net/v1/icon/icon-search.svg\",\n                alt: \"Search\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Header", "className", "children", "href", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "type", "placeholder", "_c", "$RefreshReg$"], "sources": ["E:/Hoc_Python/FastAPI_Hoc/Giao_Dien_tintuc/vietnamnet-clone/src/components/Header.js"], "sourcesContent": ["import React from 'react';\nimport './Header.css';\n\nconst Header = () => {\n  return (\n    <header className=\"header\">\n      <div className=\"header-container\">\n        {/* Top bar with hashtags and links */}\n        <div className=\"header-top\">\n          <div className=\"header-hashtags\">\n            <a href=\"#\" className=\"hashtag\"># <PERSON><PERSON><PERSON><PERSON> chuẩn ĐH-CĐ</a>\n          </div>\n          <div className=\"header-links\">\n            <a href=\"#\" className=\"header-link\">Podcast</a>\n            <a href=\"#\" className=\"header-link\">Tuần Việt Nam</a>\n            <a href=\"#\" className=\"header-link premium\">\n              <img src=\"https://vnn-res.vgcloud.vn/ResV9/images/vpremium-menu-logo.svg\" alt=\"Premium\" />\n            </a>\n            <a href=\"#\" className=\"header-link global\">\n              <img src=\"https://static.vnncdn.net/v1/icon/VietnamNet_bridge.svg\" alt=\"Global\" />\n            </a>\n          </div>\n        </div>\n\n        {/* Main header with logo and search */}\n        <div className=\"header-main\">\n          <div className=\"logo-container\">\n            <a href=\"/\" className=\"logo-link\">\n              <img \n                src=\"https://static.vnncdn.net/v1/logo/logoVietnamNet.svg\" \n                alt=\"VietNamNet\" \n                className=\"logo\"\n              />\n            </a>\n          </div>\n          \n          <div className=\"search-container\">\n            <div className=\"search-box\">\n              <input \n                type=\"text\" \n                placeholder=\"Tìm kiếm...\" \n                className=\"search-input\"\n              />\n              <button className=\"search-button\">\n                <img \n                  src=\"https://static.vnncdn.net/v1/icon/icon-search.svg\" \n                  alt=\"Search\" \n                />\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,oBACED,OAAA;IAAQE,SAAS,EAAC,QAAQ;IAAAC,QAAA,eACxBH,OAAA;MAAKE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAE/BH,OAAA;QAAKE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBH,OAAA;UAAKE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BH,OAAA;YAAGI,IAAI,EAAC,GAAG;YAACF,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eACNR,OAAA;UAAKE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BH,OAAA;YAAGI,IAAI,EAAC,GAAG;YAACF,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/CR,OAAA;YAAGI,IAAI,EAAC,GAAG;YAACF,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACrDR,OAAA;YAAGI,IAAI,EAAC,GAAG;YAACF,SAAS,EAAC,qBAAqB;YAAAC,QAAA,eACzCH,OAAA;cAAKS,GAAG,EAAC,gEAAgE;cAACC,GAAG,EAAC;YAAS;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzF,CAAC,eACJR,OAAA;YAAGI,IAAI,EAAC,GAAG;YAACF,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eACxCH,OAAA;cAAKS,GAAG,EAAC,yDAAyD;cAACC,GAAG,EAAC;YAAQ;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNR,OAAA;QAAKE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BH,OAAA;UAAKE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7BH,OAAA;YAAGI,IAAI,EAAC,GAAG;YAACF,SAAS,EAAC,WAAW;YAAAC,QAAA,eAC/BH,OAAA;cACES,GAAG,EAAC,sDAAsD;cAC1DC,GAAG,EAAC,YAAY;cAChBR,SAAS,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENR,OAAA;UAAKE,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BH,OAAA;YAAKE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBH,OAAA;cACEW,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,qBAAa;cACzBV,SAAS,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACFR,OAAA;cAAQE,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC/BH,OAAA;gBACES,GAAG,EAAC,mDAAmD;gBACvDC,GAAG,EAAC;cAAQ;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACK,EAAA,GApDIZ,MAAM;AAsDZ,eAAeA,MAAM;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
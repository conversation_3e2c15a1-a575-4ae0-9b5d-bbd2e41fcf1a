{"ast": null, "code": "var _jsxFileName = \"E:\\\\Hoc_Python\\\\FastAPI_Hoc\\\\Giao_Dien_tintuc\\\\vietnamnet-clone\\\\src\\\\components\\\\MainContent.js\";\nimport React from 'react';\nimport './MainContent.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MainContent = () => {\n  // Sample news data (in real app, this would come from API)\n  const featuredNews = {\n    title: \"<PERSON>ứ<PERSON> thuế 20% của Mỹ: Thử thách hay là động lực cho Việt Nam?\",\n    excerpt: \"Khi Nhà Trắng công bố sắc lệnh điều chỉnh thuế đối ứng với 69 quốc gia ngày 1/8, thì tại <PERSON>, nhiều nhà xuất khẩu lớn đã cảm nhận rõ làn gió đổi chiều từ thị trường Hoa Kỳ.\",\n    image: \"https://static-images.vnncdn.net/vps_images_publish/000001/000003/2025/8/3/muc-thue-20-cua-my-thu-thach-hay-la-dong-luc-cho-viet-nam-1437.jpg?width=760&s=N2mRAYZmFzT8Rl-qZnfhdg\",\n    category: \"Tuần Việt Nam\",\n    href: \"#\"\n  };\n  const sideNews = [{\n    title: \"An cư đi cùng sinh kế, mở cơ hội cho người yếu thế vươn lên\",\n    image: \"https://static-images.vnncdn.net/vps_images_publish/000001/000003/2025/8/3/an-cu-di-cung-sinh-ke-mo-co-hoi-cho-nguoi-yeu-the-vuon-len-1743.jpg?width=260&s=J3C1OM5y70YHYbWKi6PZMQ\",\n    category: \"Dân tộc và Tôn giáo\",\n    href: \"#\"\n  }, {\n    title: \"Cảnh tượng khó tin tại hồ nước từng có người chết đuối ở Ninh Bình\",\n    image: \"https://static-images.vnncdn.net/vps_images_publish/000001/000003/2025/8/3/canh-tuong-kho-tin-tai-ho-nuoc-tung-co-nguoi-chet-duoi-o-ninh-binh-1537.jpg?width=260&s=4f5o3UHmkjbDRDgTfWpk_Q\",\n    category: \"Thời sự\",\n    href: \"#\",\n    hasVideo: true\n  }, {\n    title: \"Nga nắm tuyến đường trọng yếu ở Donbass, Ukraine bổ nhiệm tư lệnh không quân mới\",\n    image: \"https://static-images.vnncdn.net/vps_images_publish/000001/000003/2025/8/4/nga-nam-tuyen-duong-trong-yeu-o-donbass-ukraine-bo-nhiem-tu-lenh-khong-quan-moi-156.jpg?width=260&s=wNqJMamykNoTk-Kf6QDF0w\",\n    category: \"Thế giới\",\n    href: \"#\"\n  }, {\n    title: \"Tuyển bóng chuyền nữ Việt Nam: Gieo hi vọng đổi ngôi số 1 Đông Nam Á\",\n    image: \"https://static-images.vnncdn.net/vps_images_publish/000001/000003/2025/8/3/tuyen-bong-chuyen-nu-viet-nam-gieo-hi-vong-doi-ngoi-so-1-dong-nam-a-1623.jpg?width=260&s=ZWDcf83hKa-eK_1ScVqXbg\",\n    category: \"Thể thao\",\n    href: \"#\",\n    hasVideo: true\n  }, {\n    title: \"Nửa thế kỷ bám vỉa hè TPHCM, gánh cháo đêm đông khách từ khuya đến rạng sáng\",\n    image: \"https://static-images.vnncdn.net/vps_images_publish/000001/000003/2025/8/3/nua-the-ky-bam-via-he-tphcm-ganh-chao-dem-dong-khach-tu-khuya-den-rang-sang-1860.jpg?width=260&s=ggB1Ov0J6PDVG5Y8AxXE5w\",\n    category: \"Đời sống\",\n    href: \"#\",\n    hasPhoto: true\n  }, {\n    title: \"Cấm xe tải đi vào làn tốc độ cao nhất trên cao tốc: Đơn vị quản lý đường nói gì?\",\n    image: \"https://static-images.vnncdn.net/vps_images_publish/000001/000003/2025/8/3/cam-xe-tai-di-lan-sat-dai-phan-cach-giua-tren-cao-toc-co-lam-kho-tai-xe-1824.jpg?width=260&s=jjB98iW61nPP9uXThKIrjg\",\n    category: \"Thời sự\",\n    href: \"#\"\n  }];\n  const mainNews = [{\n    title: \"Bên trong 'lò' luyện thợ lặn quân sự hiện đại bậc nhất Đông Nam Á\",\n    excerpt: \"Trung tâm huấn luyện lặn sâu của Lữ đoàn 126 Hải quân là nơi đào tạo những chiến sĩ thợ lặn quân sự tinh nhuệ, có khả năng tác chiến và cứu nạn ở độ sâu lớn, trong điều kiện môi trường khắc nghiệt.\",\n    image: \"https://static-images.vnncdn.net/vps_images_publish/000001/000003/2025/8/3/ben-trong-lo-luyen-tho-lan-quan-su-hien-dai-bac-nhat-dong-nam-a-1768.jpg?width=760&s=qz0WsnAm61EGdrmRapoiyA\",\n    category: \"Chính trị\",\n    href: \"#\"\n  }, {\n    title: \"Giao thông khác lạ khi đường phố ở Hà Nội được dỡ hàng loạt dải phân cách cứng\",\n    excerpt: \"Ngoài những khán đài lớn đang được lắp đặt ở quảng trường Ba Đình, nhiều dải phân cách cứng tại trung tâm Hà Nội được tháo dỡ, thay bằng vạch sơn, mở rộng đường để phục vụ Lễ kỷ niệm 80 năm Cách mạng Tháng Tám và Quốc khánh 2/9.\",\n    image: \"https://static-images.vnncdn.net/vps_images_publish/000001/000003/2025/8/4/giao-thong-khac-la-khi-duong-pho-o-ha-noi-duoc-do-hang-loat-dai-phan-cach-cung-261.jpg?width=760&s=bl2fW4QgnMA6VgpbuA_7AA\",\n    category: \"Thời sự\",\n    href: \"#\",\n    hasPhoto: true\n  }];\n  return /*#__PURE__*/_jsxDEV(\"main\", {\n    className: \"main-content\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"content-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"top-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"side-news\",\n          children: sideNews.map((news, index) => /*#__PURE__*/_jsxDEV(\"article\", {\n            className: \"side-news-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"news-image\",\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: news.href,\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: news.image,\n                  alt: news.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 21\n                }, this), news.hasVideo && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"media-icon video-icon\",\n                  children: \"\\uD83D\\uDCF9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 85,\n                  columnNumber: 39\n                }, this), news.hasPhoto && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"media-icon photo-icon\",\n                  children: \"\\uD83D\\uDCF7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 86,\n                  columnNumber: 39\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"news-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"news-category\",\n                children: news.category\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"news-title\",\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: news.href,\n                  children: news.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 92,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"featured-news\",\n          children: /*#__PURE__*/_jsxDEV(\"article\", {\n            className: \"featured-article\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"featured-image\",\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: featuredNews.href,\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: featuredNews.image,\n                  alt: featuredNews.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 103,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"featured-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"featured-category\",\n                children: featuredNews.category\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"featured-title\",\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: featuredNews.href,\n                  children: featuredNews.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 109,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"featured-excerpt\",\n                children: featuredNews.excerpt\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"main-news-section\",\n        children: mainNews.map((news, index) => /*#__PURE__*/_jsxDEV(\"article\", {\n          className: \"main-news-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"main-news-image\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: news.href,\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: news.image,\n                alt: news.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 19\n              }, this), news.hasPhoto && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"media-icon photo-icon\",\n                children: \"\\uD83D\\uDCF7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"main-news-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"main-news-category\",\n              children: news.category\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"main-news-title\",\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: news.href,\n                children: news.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"main-news-excerpt\",\n              children: news.excerpt\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 5\n  }, this);\n};\n_c = MainContent;\nexport default MainContent;\nvar _c;\n$RefreshReg$(_c, \"MainContent\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "MainContent", "featuredNews", "title", "excerpt", "image", "category", "href", "sideNews", "hasVideo", "hasPhoto", "mainNews", "className", "children", "map", "news", "index", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["E:/Hoc_Python/FastAPI_Hoc/Giao_Dien_tintuc/vietnamnet-clone/src/components/MainContent.js"], "sourcesContent": ["import React from 'react';\nimport './MainContent.css';\n\nconst MainContent = () => {\n  // Sample news data (in real app, this would come from API)\n  const featuredNews = {\n    title: \"<PERSON><PERSON><PERSON> thuế 20% của Mỹ: Th<PERSON> thách hay là động lực cho Việt Nam?\",\n    excerpt: \"<PERSON>hi Nhà Trắng công bố sắc lệnh điều chỉnh thuế đối ứng với 69 quốc gia ngày 1/8, thì tạ<PERSON>, nhiều nhà xuất khẩu lớn đã cảm nhận rõ làn gió đổi chiều từ thị trường Hoa Kỳ.\",\n    image: \"https://static-images.vnncdn.net/vps_images_publish/000001/000003/2025/8/3/muc-thue-20-cua-my-thu-thach-hay-la-dong-luc-cho-viet-nam-1437.jpg?width=760&s=N2mRAYZmFzT8Rl-qZnfhdg\",\n    category: \"Tuần Việt Nam\",\n    href: \"#\"\n  };\n\n  const sideNews = [\n    {\n      title: \"An cư đi cùng sinh kế, mở cơ hội cho người yếu thế vươn lên\",\n      image: \"https://static-images.vnncdn.net/vps_images_publish/000001/000003/2025/8/3/an-cu-di-cung-sinh-ke-mo-co-hoi-cho-nguoi-yeu-the-vuon-len-1743.jpg?width=260&s=J3C1OM5y70YHYbWKi6PZMQ\",\n      category: \"Dân tộc và Tôn giáo\",\n      href: \"#\"\n    },\n    {\n      title: \"Cảnh tượng khó tin tại hồ nước từng có người chết đuối ở Ninh Bình\",\n      image: \"https://static-images.vnncdn.net/vps_images_publish/000001/000003/2025/8/3/canh-tuong-kho-tin-tai-ho-nuoc-tung-co-nguoi-chet-duoi-o-ninh-binh-1537.jpg?width=260&s=4f5o3UHmkjbDRDgTfWpk_Q\",\n      category: \"Thời sự\",\n      href: \"#\",\n      hasVideo: true\n    },\n    {\n      title: \"Nga nắm tuyến đường trọng yếu ở Donbass, Ukraine bổ nhiệm tư lệnh không quân mới\",\n      image: \"https://static-images.vnncdn.net/vps_images_publish/000001/000003/2025/8/4/nga-nam-tuyen-duong-trong-yeu-o-donbass-ukraine-bo-nhiem-tu-lenh-khong-quan-moi-156.jpg?width=260&s=wNqJMamykNoTk-Kf6QDF0w\",\n      category: \"Thế giới\",\n      href: \"#\"\n    },\n    {\n      title: \"Tuyển bóng chuyền nữ Việt Nam: Gieo hi vọng đổi ngôi số 1 Đông Nam Á\",\n      image: \"https://static-images.vnncdn.net/vps_images_publish/000001/000003/2025/8/3/tuyen-bong-chuyen-nu-viet-nam-gieo-hi-vong-doi-ngoi-so-1-dong-nam-a-1623.jpg?width=260&s=ZWDcf83hKa-eK_1ScVqXbg\",\n      category: \"Thể thao\",\n      href: \"#\",\n      hasVideo: true\n    },\n    {\n      title: \"Nửa thế kỷ bám vỉa hè TPHCM, gánh cháo đêm đông khách từ khuya đến rạng sáng\",\n      image: \"https://static-images.vnncdn.net/vps_images_publish/000001/000003/2025/8/3/nua-the-ky-bam-via-he-tphcm-ganh-chao-dem-dong-khach-tu-khuya-den-rang-sang-1860.jpg?width=260&s=ggB1Ov0J6PDVG5Y8AxXE5w\",\n      category: \"Đời sống\",\n      href: \"#\",\n      hasPhoto: true\n    },\n    {\n      title: \"Cấm xe tải đi vào làn tốc độ cao nhất trên cao tốc: Đơn vị quản lý đường nói gì?\",\n      image: \"https://static-images.vnncdn.net/vps_images_publish/000001/000003/2025/8/3/cam-xe-tai-di-lan-sat-dai-phan-cach-giua-tren-cao-toc-co-lam-kho-tai-xe-1824.jpg?width=260&s=jjB98iW61nPP9uXThKIrjg\",\n      category: \"Thời sự\",\n      href: \"#\"\n    }\n  ];\n\n  const mainNews = [\n    {\n      title: \"Bên trong 'lò' luyện thợ lặn quân sự hiện đại bậc nhất Đông Nam Á\",\n      excerpt: \"Trung tâm huấn luyện lặn sâu của Lữ đoàn 126 Hải quân là nơi đào tạo những chiến sĩ thợ lặn quân sự tinh nhuệ, có khả năng tác chiến và cứu nạn ở độ sâu lớn, trong điều kiện môi trường khắc nghiệt.\",\n      image: \"https://static-images.vnncdn.net/vps_images_publish/000001/000003/2025/8/3/ben-trong-lo-luyen-tho-lan-quan-su-hien-dai-bac-nhat-dong-nam-a-1768.jpg?width=760&s=qz0WsnAm61EGdrmRapoiyA\",\n      category: \"Chính trị\",\n      href: \"#\"\n    },\n    {\n      title: \"Giao thông khác lạ khi đường phố ở Hà Nội được dỡ hàng loạt dải phân cách cứng\",\n      excerpt: \"Ngoài những khán đài lớn đang được lắp đặt ở quảng trường Ba Đình, nhiều dải phân cách cứng tại trung tâm Hà Nội được tháo dỡ, thay bằng vạch sơn, mở rộng đường để phục vụ Lễ kỷ niệm 80 năm Cách mạng Tháng Tám và Quốc khánh 2/9.\",\n      image: \"https://static-images.vnncdn.net/vps_images_publish/000001/000003/2025/8/4/giao-thong-khac-la-khi-duong-pho-o-ha-noi-duoc-do-hang-loat-dai-phan-cach-cung-261.jpg?width=760&s=bl2fW4QgnMA6VgpbuA_7AA\",\n      category: \"Thời sự\",\n      href: \"#\",\n      hasPhoto: true\n    }\n  ];\n\n  return (\n    <main className=\"main-content\">\n      <div className=\"content-container\">\n        {/* Top Section with Featured and Side News */}\n        <section className=\"top-section\">\n          <div className=\"side-news\">\n            {sideNews.map((news, index) => (\n              <article key={index} className=\"side-news-item\">\n                <div className=\"news-image\">\n                  <a href={news.href}>\n                    <img src={news.image} alt={news.title} />\n                    {news.hasVideo && <span className=\"media-icon video-icon\">📹</span>}\n                    {news.hasPhoto && <span className=\"media-icon photo-icon\">📷</span>}\n                  </a>\n                </div>\n                <div className=\"news-content\">\n                  <span className=\"news-category\">{news.category}</span>\n                  <h3 className=\"news-title\">\n                    <a href={news.href}>{news.title}</a>\n                  </h3>\n                </div>\n              </article>\n            ))}\n          </div>\n\n          <div className=\"featured-news\">\n            <article className=\"featured-article\">\n              <div className=\"featured-image\">\n                <a href={featuredNews.href}>\n                  <img src={featuredNews.image} alt={featuredNews.title} />\n                </a>\n              </div>\n              <div className=\"featured-content\">\n                <span className=\"featured-category\">{featuredNews.category}</span>\n                <h1 className=\"featured-title\">\n                  <a href={featuredNews.href}>{featuredNews.title}</a>\n                </h1>\n                <p className=\"featured-excerpt\">{featuredNews.excerpt}</p>\n              </div>\n            </article>\n          </div>\n        </section>\n\n        {/* Main News Section */}\n        <section className=\"main-news-section\">\n          {mainNews.map((news, index) => (\n            <article key={index} className=\"main-news-item\">\n              <div className=\"main-news-image\">\n                <a href={news.href}>\n                  <img src={news.image} alt={news.title} />\n                  {news.hasPhoto && <span className=\"media-icon photo-icon\">📷</span>}\n                </a>\n              </div>\n              <div className=\"main-news-content\">\n                <span className=\"main-news-category\">{news.category}</span>\n                <h2 className=\"main-news-title\">\n                  <a href={news.href}>{news.title}</a>\n                </h2>\n                <p className=\"main-news-excerpt\">{news.excerpt}</p>\n              </div>\n            </article>\n          ))}\n        </section>\n      </div>\n    </main>\n  );\n};\n\nexport default MainContent;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAA,KAAM;EACxB;EACA,MAAMC,YAAY,GAAG;IACnBC,KAAK,EAAE,8DAA8D;IACrEC,OAAO,EAAE,iLAAiL;IAC1LC,KAAK,EAAE,kLAAkL;IACzLC,QAAQ,EAAE,eAAe;IACzBC,IAAI,EAAE;EACR,CAAC;EAED,MAAMC,QAAQ,GAAG,CACf;IACEL,KAAK,EAAE,6DAA6D;IACpEE,KAAK,EAAE,mLAAmL;IAC1LC,QAAQ,EAAE,qBAAqB;IAC/BC,IAAI,EAAE;EACR,CAAC,EACD;IACEJ,KAAK,EAAE,oEAAoE;IAC3EE,KAAK,EAAE,2LAA2L;IAClMC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,GAAG;IACTE,QAAQ,EAAE;EACZ,CAAC,EACD;IACEN,KAAK,EAAE,kFAAkF;IACzFE,KAAK,EAAE,uMAAuM;IAC9MC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE;EACR,CAAC,EACD;IACEJ,KAAK,EAAE,sEAAsE;IAC7EE,KAAK,EAAE,4LAA4L;IACnMC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,GAAG;IACTE,QAAQ,EAAE;EACZ,CAAC,EACD;IACEN,KAAK,EAAE,8EAA8E;IACrFE,KAAK,EAAE,oMAAoM;IAC3MC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,GAAG;IACTG,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,KAAK,EAAE,kFAAkF;IACzFE,KAAK,EAAE,gMAAgM;IACvMC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE;EACR,CAAC,CACF;EAED,MAAMI,QAAQ,GAAG,CACf;IACER,KAAK,EAAE,mEAAmE;IAC1EC,OAAO,EAAE,uMAAuM;IAChNC,KAAK,EAAE,wLAAwL;IAC/LC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE;EACR,CAAC,EACD;IACEJ,KAAK,EAAE,gFAAgF;IACvFC,OAAO,EAAE,sOAAsO;IAC/OC,KAAK,EAAE,sMAAsM;IAC7MC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,GAAG;IACTG,QAAQ,EAAE;EACZ,CAAC,CACF;EAED,oBACEV,OAAA;IAAMY,SAAS,EAAC,cAAc;IAAAC,QAAA,eAC5Bb,OAAA;MAAKY,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAEhCb,OAAA;QAASY,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC9Bb,OAAA;UAAKY,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBL,QAAQ,CAACM,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACxBhB,OAAA;YAAqBY,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7Cb,OAAA;cAAKY,SAAS,EAAC,YAAY;cAAAC,QAAA,eACzBb,OAAA;gBAAGO,IAAI,EAAEQ,IAAI,CAACR,IAAK;gBAAAM,QAAA,gBACjBb,OAAA;kBAAKiB,GAAG,EAAEF,IAAI,CAACV,KAAM;kBAACa,GAAG,EAAEH,IAAI,CAACZ;gBAAM;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACxCP,IAAI,CAACN,QAAQ,iBAAIT,OAAA;kBAAMY,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAE;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAClEP,IAAI,CAACL,QAAQ,iBAAIV,OAAA;kBAAMY,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAE;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNtB,OAAA;cAAKY,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3Bb,OAAA;gBAAMY,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEE,IAAI,CAACT;cAAQ;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtDtB,OAAA;gBAAIY,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACxBb,OAAA;kBAAGO,IAAI,EAAEQ,IAAI,CAACR,IAAK;kBAAAM,QAAA,EAAEE,IAAI,CAACZ;gBAAK;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA,GAbMN,KAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcV,CACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENtB,OAAA;UAAKY,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5Bb,OAAA;YAASY,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBACnCb,OAAA;cAAKY,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC7Bb,OAAA;gBAAGO,IAAI,EAAEL,YAAY,CAACK,IAAK;gBAAAM,QAAA,eACzBb,OAAA;kBAAKiB,GAAG,EAAEf,YAAY,CAACG,KAAM;kBAACa,GAAG,EAAEhB,YAAY,CAACC;gBAAM;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNtB,OAAA;cAAKY,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/Bb,OAAA;gBAAMY,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAEX,YAAY,CAACI;cAAQ;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClEtB,OAAA;gBAAIY,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,eAC5Bb,OAAA;kBAAGO,IAAI,EAAEL,YAAY,CAACK,IAAK;kBAAAM,QAAA,EAAEX,YAAY,CAACC;gBAAK;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACLtB,OAAA;gBAAGY,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAEX,YAAY,CAACE;cAAO;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGVtB,OAAA;QAASY,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EACnCF,QAAQ,CAACG,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACxBhB,OAAA;UAAqBY,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7Cb,OAAA;YAAKY,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9Bb,OAAA;cAAGO,IAAI,EAAEQ,IAAI,CAACR,IAAK;cAAAM,QAAA,gBACjBb,OAAA;gBAAKiB,GAAG,EAAEF,IAAI,CAACV,KAAM;gBAACa,GAAG,EAAEH,IAAI,CAACZ;cAAM;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACxCP,IAAI,CAACL,QAAQ,iBAAIV,OAAA;gBAAMY,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNtB,OAAA;YAAKY,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCb,OAAA;cAAMY,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAEE,IAAI,CAACT;YAAQ;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3DtB,OAAA;cAAIY,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC7Bb,OAAA;gBAAGO,IAAI,EAAEQ,IAAI,CAACR,IAAK;gBAAAM,QAAA,EAAEE,IAAI,CAACZ;cAAK;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACLtB,OAAA;cAAGY,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAEE,IAAI,CAACX;YAAO;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA,GAbMN,KAAK;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAcV,CACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAACC,EAAA,GAxIItB,WAAW;AA0IjB,eAAeA,WAAW;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
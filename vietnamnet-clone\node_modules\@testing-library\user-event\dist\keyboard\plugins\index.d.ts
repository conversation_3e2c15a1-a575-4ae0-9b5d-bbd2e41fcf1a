import { behaviorPlugin } from '../types';
export declare const replaceBehavior: behaviorPlugin[];
export declare const preKeydownBehavior: behaviorPlugin[];
export declare const keydownBehavior: behaviorPlugin[];
export declare const keypressBehavior: behaviorPlugin[];
export declare const preKeyupBehavior: behaviorPlugin[];
export declare const keyupBehavior: behaviorPlugin[];
export declare const postKeyupBehavior: behaviorPlugin[];

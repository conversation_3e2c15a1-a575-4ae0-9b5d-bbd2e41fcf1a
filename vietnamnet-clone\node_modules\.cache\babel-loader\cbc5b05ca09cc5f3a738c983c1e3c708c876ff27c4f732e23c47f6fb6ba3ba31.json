{"ast": null, "code": "var _jsxFileName = \"E:\\\\Hoc_Python\\\\FastAPI_Hoc\\\\Giao_Dien_tintuc\\\\vietnamnet-clone\\\\src\\\\components\\\\Navigation.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport './Navigation.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Navigation = () => {\n  _s();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const menuItems = [{\n    title: 'Chính trị',\n    href: '/chinh-tri'\n  }, {\n    title: 'Thời sự',\n    href: '/thoi-su'\n  }, {\n    title: 'Kinh doanh',\n    href: '/kinh-doanh'\n  }, {\n    title: 'Dân tộc và Tôn giáo',\n    href: '/dan-toc-ton-giao'\n  }, {\n    title: '<PERSON>h<PERSON> thao',\n    href: '/the-thao'\n  }, {\n    title: '<PERSON><PERSON><PERSON><PERSON> d<PERSON>',\n    href: '/giao-duc'\n  }, {\n    title: 'Thế giới',\n    href: '/the-gioi'\n  }, {\n    title: '<PERSON>ời sống',\n    href: '/doi-song'\n  }, {\n    title: 'Văn hóa - Giải trí',\n    href: '/van-hoa-giai-tri'\n  }, {\n    title: 'Sức khỏe',\n    href: '/suc-khoe'\n  }, {\n    title: 'Công nghệ',\n    href: '/cong-nghe'\n  }, {\n    title: 'Pháp luật',\n    href: '/phap-luat'\n  }, {\n    title: 'Xe',\n    href: '/oto-xe-may'\n  }, {\n    title: 'Bất động sản',\n    href: '/bat-dong-san'\n  }, {\n    title: 'Du lịch',\n    href: '/du-lich'\n  }, {\n    title: 'Bạn đọc',\n    href: '/ban-doc'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"navigation\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"nav-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-desktop\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"nav-menu\",\n          children: menuItems.map((item, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: item.href,\n              className: \"nav-link\",\n              children: item.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 17\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-mobile\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"mobile-menu-toggle\",\n          onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), isMobileMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mobile-menu\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"mobile-nav-menu\",\n          children: menuItems.map((item, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"mobile-nav-item\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: item.href,\n              className: \"mobile-nav-link\",\n              children: item.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n};\n_s(Navigation, \"QerECOS75+B7gv+k3q7FrDf39mc=\");\n_c = Navigation;\nexport default Navigation;\nvar _c;\n$RefreshReg$(_c, \"Navigation\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "Navigation", "_s", "isMobileMenuOpen", "setIsMobileMenuOpen", "menuItems", "title", "href", "className", "children", "map", "item", "index", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["E:/Hoc_Python/FastAPI_Hoc/Giao_Dien_tintuc/vietnamnet-clone/src/components/Navigation.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport './Navigation.css';\n\nconst Navigation = () => {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  const menuItems = [\n    { title: 'Chính trị', href: '/chinh-tri' },\n    { title: 'Thời sự', href: '/thoi-su' },\n    { title: 'Kinh doanh', href: '/kinh-doanh' },\n    { title: '<PERSON><PERSON> tộc và Tôn giáo', href: '/dan-toc-ton-giao' },\n    { title: 'Thể thao', href: '/the-thao' },\n    { title: '<PERSON><PERSON><PERSON><PERSON> dục', href: '/giao-duc' },\n    { title: 'Thế giới', href: '/the-gioi' },\n    { title: 'Đời sống', href: '/doi-song' },\n    { title: '<PERSON><PERSON>n hóa - <PERSON>i<PERSON> trí', href: '/van-hoa-giai-tri' },\n    { title: '<PERSON>ức khỏe', href: '/suc-khoe' },\n    { title: '<PERSON>ông nghệ', href: '/cong-nghe' },\n    { title: 'Pháp luật', href: '/phap-luat' },\n    { title: 'Xe', href: '/oto-xe-may' },\n    { title: 'Bất động sản', href: '/bat-dong-san' },\n    { title: 'Du lịch', href: '/du-lich' },\n    { title: 'Bạn đọc', href: '/ban-doc' }\n  ];\n\n  return (\n    <nav className=\"navigation\">\n      <div className=\"nav-container\">\n        {/* Desktop Navigation */}\n        <div className=\"nav-desktop\">\n          <ul className=\"nav-menu\">\n            {menuItems.map((item, index) => (\n              <li key={index} className=\"nav-item\">\n                <a href={item.href} className=\"nav-link\">\n                  {item.title}\n                </a>\n              </li>\n            ))}\n          </ul>\n        </div>\n\n        {/* Mobile Navigation Toggle */}\n        <div className=\"nav-mobile\">\n          <button \n            className=\"mobile-menu-toggle\"\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n          >\n            <span></span>\n            <span></span>\n            <span></span>\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMobileMenuOpen && (\n          <div className=\"mobile-menu\">\n            <ul className=\"mobile-nav-menu\">\n              {menuItems.map((item, index) => (\n                <li key={index} className=\"mobile-nav-item\">\n                  <a href={item.href} className=\"mobile-nav-link\">\n                    {item.title}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGN,QAAQ,CAAC,KAAK,CAAC;EAE/D,MAAMO,SAAS,GAAG,CAChB;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAa,CAAC,EAC1C;IAAED,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAW,CAAC,EACtC;IAAED,KAAK,EAAE,YAAY;IAAEC,IAAI,EAAE;EAAc,CAAC,EAC5C;IAAED,KAAK,EAAE,qBAAqB;IAAEC,IAAI,EAAE;EAAoB,CAAC,EAC3D;IAAED,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAY,CAAC,EACxC;IAAED,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAY,CAAC,EACxC;IAAED,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAY,CAAC,EACxC;IAAED,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAY,CAAC,EACxC;IAAED,KAAK,EAAE,oBAAoB;IAAEC,IAAI,EAAE;EAAoB,CAAC,EAC1D;IAAED,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAY,CAAC,EACxC;IAAED,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAa,CAAC,EAC1C;IAAED,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAa,CAAC,EAC1C;IAAED,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE;EAAc,CAAC,EACpC;IAAED,KAAK,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAgB,CAAC,EAChD;IAAED,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAW,CAAC,EACtC;IAAED,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAW,CAAC,CACvC;EAED,oBACEP,OAAA;IAAKQ,SAAS,EAAC,YAAY;IAAAC,QAAA,eACzBT,OAAA;MAAKQ,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAE5BT,OAAA;QAAKQ,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BT,OAAA;UAAIQ,SAAS,EAAC,UAAU;UAAAC,QAAA,EACrBJ,SAAS,CAACK,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzBZ,OAAA;YAAgBQ,SAAS,EAAC,UAAU;YAAAC,QAAA,eAClCT,OAAA;cAAGO,IAAI,EAAEI,IAAI,CAACJ,IAAK;cAACC,SAAS,EAAC,UAAU;cAAAC,QAAA,EACrCE,IAAI,CAACL;YAAK;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC,GAHGJ,KAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIV,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGNhB,OAAA;QAAKQ,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzBT,OAAA;UACEQ,SAAS,EAAC,oBAAoB;UAC9BS,OAAO,EAAEA,CAAA,KAAMb,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;UAAAM,QAAA,gBAEtDT,OAAA;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbhB,OAAA;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbhB,OAAA;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLb,gBAAgB,iBACfH,OAAA;QAAKQ,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BT,OAAA;UAAIQ,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC5BJ,SAAS,CAACK,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzBZ,OAAA;YAAgBQ,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eACzCT,OAAA;cAAGO,IAAI,EAAEI,IAAI,CAACJ,IAAK;cAACC,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC5CE,IAAI,CAACL;YAAK;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC,GAHGJ,KAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIV,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACd,EAAA,CAnEID,UAAU;AAAAiB,EAAA,GAAVjB,UAAU;AAqEhB,eAAeA,UAAU;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
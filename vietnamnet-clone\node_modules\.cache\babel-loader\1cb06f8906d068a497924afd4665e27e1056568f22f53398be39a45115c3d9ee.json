{"ast": null, "code": "var _jsxFileName = \"E:\\\\Hoc_Python\\\\FastAPI_Hoc\\\\Giao_Dien_tintuc\\\\vietnamnet-clone\\\\src\\\\components\\\\Footer.js\";\nimport React from 'react';\nimport './Footer.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"footer\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"footer-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-main\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-logo\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"https://static.vnncdn.net/v1/logo/logoVietnamNet.svg\",\n              alt: \"VietNamNet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 13,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"C\\u01A1 quan ch\\u1EE7 qu\\u1EA3n:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 19,\n                columnNumber: 18\n              }, this), \" B\\u1ED9 D\\xE2n t\\u1ED9c v\\xE0 T\\xF4n gi\\xE1o\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"S\\u1ED1 gi\\u1EA5y ph\\xE9p:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 20,\n                columnNumber: 18\n              }, this), \" 09/GP - BTTTT, c\\u1EA5p ng\\xE0y 07/01/2019\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"T\\u1ED5ng bi\\xEAn t\\u1EADp:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 21,\n                columnNumber: 18\n              }, this), \" Nguy\\u1EC5n V\\u0103n B\\xE1\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Li\\xEAn h\\u1EC7 t\\xF2a so\\u1EA1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u0110\\u1ECBa ch\\u1EC9:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 18\n              }, this), \" T\\u1EA7ng 18, To\\xE0 nh\\xE0 C\\u1EE5c Vi\\u1EC5n th\\xF4ng (VNTA), 68 D\\u01B0\\u01A1ng \\u0110\\xECnh Ngh\\u1EC7, ph\\u01B0\\u1EDDng C\\u1EA7u Gi\\u1EA5y, TP. H\\xE0 N\\u1ED9i.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u0110i\\u1EC7n tho\\u1EA1i:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 18\n              }, this), \" 02439369898 - Hotline: 0923457788\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Email:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 18\n              }, this), \" <EMAIL>\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Li\\xEAn h\\u1EC7 qu\\u1EA3ng c\\xE1o\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"C\\xF4ng ty:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 18\n              }, this), \" C\\xF4ng ty C\\u1ED5 ph\\u1EA7n Truy\\u1EC1n th\\xF4ng VietNamNet\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Hotline:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 18\n              }, this), \" 0919 405 885 (H\\xE0 N\\u1ED9i) - 0919 435 885 (Tp.HCM)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Email:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 18\n              }, this), \" <EMAIL>\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"B\\xE1o gi\\xE1:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 18\n              }, this), \" \", /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"http://vads.vn\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                children: \"http://vads.vn\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 44\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Li\\xEAn k\\u1EBFt\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quick-links\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"footer-link\",\n              children: \"T\\u1EA3i \\u1EE9ng d\\u1EE5ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"footer-link\",\n              children: \"\\u0110\\u1ED9c gi\\u1EA3 g\\u1EEDi b\\xE0i\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"footer-link\",\n              children: \"Tuy\\u1EC3n d\\u1EE5ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"footer-link\",\n              children: \"L\\u1ECBch v\\u1EA1n ni\\xEAn\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-social\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Theo d\\xF5i VietNamNet tr\\xEAn\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"social-links\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            className: \"social-link facebook\",\n            \"aria-label\": \"Facebook\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"24\",\n              height: \"24\",\n              viewBox: \"0 0 24 24\",\n              fill: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            className: \"social-link youtube\",\n            \"aria-label\": \"YouTube\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"24\",\n              height: \"24\",\n              viewBox: \"0 0 24 24\",\n              fill: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            className: \"social-link twitter\",\n            \"aria-label\": \"Twitter\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"24\",\n              height: \"24\",\n              viewBox: \"0 0 24 24\",\n              fill: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            className: \"social-link tiktok\",\n            \"aria-label\": \"TikTok\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"24\",\n              height: \"24\",\n              viewBox: \"0 0 24 24\",\n              fill: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-bottom\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"copyright\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\xA9 1997 B\\xE1o VietNamNet. All rights reserved.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Ch\\u1EC9 \\u0111\\u01B0\\u1EE3c ph\\xE1t h\\xE0nh l\\u1EA1i th\\xF4ng tin t\\u1EEB website n\\xE0y khi c\\xF3 s\\u1EF1 \\u0111\\u1ED3ng \\xFD b\\u1EB1ng v\\u0103n b\\u1EA3n c\\u1EE7a b\\xE1o VietNamNet.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tech-support\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"H\\u1ED7 tr\\u1EE3 k\\u1EF9 thu\\u1EADt:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 16\n            }, this), \" <EMAIL>\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Footer", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "target", "rel", "width", "height", "viewBox", "fill", "d", "_c", "$RefreshReg$"], "sources": ["E:/Hoc_Python/FastAPI_Hoc/Giao_Dien_tintuc/vietnamnet-clone/src/components/Footer.js"], "sourcesContent": ["import React from 'react';\nimport './Footer.css';\n\nconst Footer = () => {\n  return (\n    <footer className=\"footer\">\n      <div className=\"footer-container\">\n        {/* Main Footer Content */}\n        <div className=\"footer-main\">\n          {/* Logo and Info */}\n          <div className=\"footer-section\">\n            <div className=\"footer-logo\">\n              <img \n                src=\"https://static.vnncdn.net/v1/logo/logoVietnamNet.svg\" \n                alt=\"VietNamNet\" \n              />\n            </div>\n            <div className=\"footer-info\">\n              <p><strong><PERSON><PERSON> quan chủ quản:</strong> B<PERSON> Dân tộc và Tôn giáo</p>\n              <p><strong>Số giấy phép:</strong> 09/GP - BTTTT, cấp ngày 07/01/2019</p>\n              <p><strong>Tổng biên tập:</strong> <PERSON><PERSON><PERSON><PERSON></p>\n            </div>\n          </div>\n\n          {/* Contact Info */}\n          <div className=\"footer-section\">\n            <h4><PERSON><PERSON><PERSON> hệ tòa soạn</h4>\n            <div className=\"contact-info\">\n              <p><strong>Đ<PERSON>a chỉ:</strong> Tầng 18, Toà nhà Cục Viễn thông (VNTA), 68 Dương Đình Nghệ, phường Cầu Giấy, TP. Hà Nội.</p>\n              <p><strong>Điện thoại:</strong> 02439369898 - Hotline: 0923457788</p>\n              <p><strong>Email:</strong> <EMAIL></p>\n            </div>\n          </div>\n\n          {/* Advertising */}\n          <div className=\"footer-section\">\n            <h4>Liên hệ quảng cáo</h4>\n            <div className=\"contact-info\">\n              <p><strong>Công ty:</strong> Công ty Cổ phần Truyền thông VietNamNet</p>\n              <p><strong>Hotline:</strong> 0919 405 885 (Hà Nội) - 0919 435 885 (Tp.HCM)</p>\n              <p><strong>Email:</strong> <EMAIL></p>\n              <p><strong>Báo giá:</strong> <a href=\"http://vads.vn\" target=\"_blank\" rel=\"noopener noreferrer\">http://vads.vn</a></p>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div className=\"footer-section\">\n            <h4>Liên kết</h4>\n            <div className=\"quick-links\">\n              <a href=\"#\" className=\"footer-link\">Tải ứng dụng</a>\n              <a href=\"#\" className=\"footer-link\">Độc giả gửi bài</a>\n              <a href=\"#\" className=\"footer-link\">Tuyển dụng</a>\n              <a href=\"#\" className=\"footer-link\">Lịch vạn niên</a>\n            </div>\n          </div>\n        </div>\n\n        {/* Social Media */}\n        <div className=\"footer-social\">\n          <h4>Theo dõi VietNamNet trên</h4>\n          <div className=\"social-links\">\n            <a href=\"#\" className=\"social-link facebook\" aria-label=\"Facebook\">\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n              </svg>\n            </a>\n            <a href=\"#\" className=\"social-link youtube\" aria-label=\"YouTube\">\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z\"/>\n              </svg>\n            </a>\n            <a href=\"#\" className=\"social-link twitter\" aria-label=\"Twitter\">\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"/>\n              </svg>\n            </a>\n            <a href=\"#\" className=\"social-link tiktok\" aria-label=\"TikTok\">\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z\"/>\n              </svg>\n            </a>\n          </div>\n        </div>\n\n        {/* Copyright */}\n        <div className=\"footer-bottom\">\n          <div className=\"copyright\">\n            <p>© 1997 Báo VietNamNet. All rights reserved.</p>\n            <p>Chỉ được phát hành lại thông tin từ website này khi có sự đồng ý bằng văn bản của báo VietNamNet.</p>\n          </div>\n          <div className=\"tech-support\">\n            <p><strong>Hỗ trợ kỹ thuật:</strong> <EMAIL></p>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,oBACED,OAAA;IAAQE,SAAS,EAAC,QAAQ;IAAAC,QAAA,eACxBH,OAAA;MAAKE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAE/BH,OAAA;QAAKE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAE1BH,OAAA;UAAKE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BH,OAAA;YAAKE,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1BH,OAAA;cACEI,GAAG,EAAC,sDAAsD;cAC1DC,GAAG,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNT,OAAA;YAAKE,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BH,OAAA;cAAAG,QAAA,gBAAGH,OAAA;gBAAAG,QAAA,EAAQ;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,iDAAuB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAChET,OAAA;cAAAG,QAAA,gBAAGH,OAAA;gBAAAG,QAAA,EAAQ;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,+CAAmC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxET,OAAA;cAAAG,QAAA,gBAAGH,OAAA;gBAAAG,QAAA,EAAQ;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,+BAAc;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNT,OAAA;UAAKE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BH,OAAA;YAAAG,QAAA,EAAI;UAAgB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBT,OAAA;YAAKE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BH,OAAA;cAAAG,QAAA,gBAAGH,OAAA;gBAAAG,QAAA,EAAQ;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,wKAAyF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACzHT,OAAA;cAAAG,QAAA,gBAAGH,OAAA;gBAAAG,QAAA,EAAQ;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,sCAAkC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrET,OAAA;cAAAG,QAAA,gBAAGH,OAAA;gBAAAG,QAAA,EAAQ;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,6BAAyB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNT,OAAA;UAAKE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BH,OAAA;YAAAG,QAAA,EAAI;UAAiB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BT,OAAA;YAAKE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BH,OAAA;cAAAG,QAAA,gBAAGH,OAAA;gBAAAG,QAAA,EAAQ;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,iEAAwC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxET,OAAA;cAAAG,QAAA,gBAAGH,OAAA;gBAAAG,QAAA,EAAQ;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,0DAA8C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9ET,OAAA;cAAAG,QAAA,gBAAGH,OAAA;gBAAAG,QAAA,EAAQ;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,0BAAsB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpDT,OAAA;cAAAG,QAAA,gBAAGH,OAAA;gBAAAG,QAAA,EAAQ;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,eAAAT,OAAA;gBAAGU,IAAI,EAAC,gBAAgB;gBAACC,MAAM,EAAC,QAAQ;gBAACC,GAAG,EAAC,qBAAqB;gBAAAT,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNT,OAAA;UAAKE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BH,OAAA;YAAAG,QAAA,EAAI;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjBT,OAAA;YAAKE,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BH,OAAA;cAAGU,IAAI,EAAC,GAAG;cAACR,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpDT,OAAA;cAAGU,IAAI,EAAC,GAAG;cAACR,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACvDT,OAAA;cAAGU,IAAI,EAAC,GAAG;cAACR,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAClDT,OAAA;cAAGU,IAAI,EAAC,GAAG;cAACR,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNT,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BH,OAAA;UAAAG,QAAA,EAAI;QAAwB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjCT,OAAA;UAAKE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BH,OAAA;YAAGU,IAAI,EAAC,GAAG;YAACR,SAAS,EAAC,sBAAsB;YAAC,cAAW,UAAU;YAAAC,QAAA,eAChEH,OAAA;cAAKa,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,cAAc;cAAAb,QAAA,eACjEH,OAAA;gBAAMiB,CAAC,EAAC;cAAgS;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACJT,OAAA;YAAGU,IAAI,EAAC,GAAG;YAACR,SAAS,EAAC,qBAAqB;YAAC,cAAW,SAAS;YAAAC,QAAA,eAC9DH,OAAA;cAAKa,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,cAAc;cAAAb,QAAA,eACjEH,OAAA;gBAAMiB,CAAC,EAAC;cAA8V;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrW;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACJT,OAAA;YAAGU,IAAI,EAAC,GAAG;YAACR,SAAS,EAAC,qBAAqB;YAAC,cAAW,SAAS;YAAAC,QAAA,eAC9DH,OAAA;cAAKa,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,cAAc;cAAAb,QAAA,eACjEH,OAAA;gBAAMiB,CAAC,EAAC;cAA6e;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpf;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACJT,OAAA;YAAGU,IAAI,EAAC,GAAG;YAACR,SAAS,EAAC,oBAAoB;YAAC,cAAW,QAAQ;YAAAC,QAAA,eAC5DH,OAAA;cAAKa,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,cAAc;cAAAb,QAAA,eACjEH,OAAA;gBAAMiB,CAAC,EAAC;cAAulB;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9lB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNT,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BH,OAAA;UAAKE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBH,OAAA;YAAAG,QAAA,EAAG;UAA2C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClDT,OAAA;YAAAG,QAAA,EAAG;UAAiG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrG,CAAC,eACNT,OAAA;UAAKE,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BH,OAAA;YAAAG,QAAA,gBAAGH,OAAA;cAAAG,QAAA,EAAQ;YAAgB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,+BAA2B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACS,EAAA,GA9FIjB,MAAM;AAgGZ,eAAeA,MAAM;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
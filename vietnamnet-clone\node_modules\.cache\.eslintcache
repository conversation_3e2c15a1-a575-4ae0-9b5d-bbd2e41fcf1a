[{"E:\\Hoc_Python\\FastAPI_Hoc\\Giao_Dien_tintuc\\vietnamnet-clone\\src\\index.js": "1", "E:\\Hoc_Python\\FastAPI_Hoc\\Giao_Dien_tintuc\\vietnamnet-clone\\src\\App.js": "2", "E:\\Hoc_Python\\FastAPI_Hoc\\Giao_Dien_tintuc\\vietnamnet-clone\\src\\reportWebVitals.js": "3", "E:\\Hoc_Python\\FastAPI_Hoc\\Giao_Dien_tintuc\\vietnamnet-clone\\src\\components\\Header.js": "4", "E:\\Hoc_Python\\FastAPI_Hoc\\Giao_Dien_tintuc\\vietnamnet-clone\\src\\components\\Footer.js": "5", "E:\\Hoc_Python\\FastAPI_Hoc\\Giao_Dien_tintuc\\vietnamnet-clone\\src\\components\\Navigation.js": "6", "E:\\Hoc_Python\\FastAPI_Hoc\\Giao_Dien_tintuc\\vietnamnet-clone\\src\\components\\MainContent.js": "7"}, {"size": 535, "mtime": 1754272132833, "results": "8", "hashOfConfig": "9"}, {"size": 404, "mtime": 1754272200701, "results": "10", "hashOfConfig": "9"}, {"size": 362, "mtime": 1754272132986, "results": "11", "hashOfConfig": "9"}, {"size": 1905, "mtime": 1754272218378, "results": "12", "hashOfConfig": "9"}, {"size": 5840, "mtime": 1754272389276, "results": "13", "hashOfConfig": "9"}, {"size": 2350, "mtime": 1754272246712, "results": "14", "hashOfConfig": "9"}, {"size": 7262, "mtime": 1754272318560, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "cwvs6k", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\Hoc_Python\\FastAPI_Hoc\\Giao_Dien_tintuc\\vietnamnet-clone\\src\\index.js", [], [], "E:\\Hoc_Python\\FastAPI_Hoc\\Giao_Dien_tintuc\\vietnamnet-clone\\src\\App.js", [], [], "E:\\Hoc_Python\\FastAPI_Hoc\\Giao_Dien_tintuc\\vietnamnet-clone\\src\\reportWebVitals.js", [], [], "E:\\Hoc_Python\\FastAPI_Hoc\\Giao_Dien_tintuc\\vietnamnet-clone\\src\\components\\Header.js", ["37", "38", "39", "40", "41"], [], "E:\\Hoc_Python\\FastAPI_Hoc\\Giao_Dien_tintuc\\vietnamnet-clone\\src\\components\\Footer.js", ["42", "43", "44", "45", "46", "47", "48", "49"], [], "E:\\Hoc_Python\\FastAPI_Hoc\\Giao_Dien_tintuc\\vietnamnet-clone\\src\\components\\Navigation.js", [], [], "E:\\Hoc_Python\\FastAPI_Hoc\\Giao_Dien_tintuc\\vietnamnet-clone\\src\\components\\MainContent.js", [], [], {"ruleId": "50", "severity": 1, "message": "51", "line": 11, "column": 13, "nodeType": "52", "endLine": 11, "endColumn": 45}, {"ruleId": "50", "severity": 1, "message": "51", "line": 14, "column": 13, "nodeType": "52", "endLine": 14, "endColumn": 49}, {"ruleId": "50", "severity": 1, "message": "51", "line": 15, "column": 13, "nodeType": "52", "endLine": 15, "endColumn": 49}, {"ruleId": "50", "severity": 1, "message": "51", "line": 16, "column": 13, "nodeType": "52", "endLine": 16, "endColumn": 57}, {"ruleId": "50", "severity": 1, "message": "51", "line": 19, "column": 13, "nodeType": "52", "endLine": 19, "endColumn": 56}, {"ruleId": "50", "severity": 1, "message": "51", "line": 50, "column": 15, "nodeType": "52", "endLine": 50, "endColumn": 51}, {"ruleId": "50", "severity": 1, "message": "51", "line": 51, "column": 15, "nodeType": "52", "endLine": 51, "endColumn": 51}, {"ruleId": "50", "severity": 1, "message": "51", "line": 52, "column": 15, "nodeType": "52", "endLine": 52, "endColumn": 51}, {"ruleId": "50", "severity": 1, "message": "51", "line": 53, "column": 15, "nodeType": "52", "endLine": 53, "endColumn": 51}, {"ruleId": "50", "severity": 1, "message": "51", "line": 62, "column": 13, "nodeType": "52", "endLine": 62, "endColumn": 80}, {"ruleId": "50", "severity": 1, "message": "51", "line": 67, "column": 13, "nodeType": "52", "endLine": 67, "endColumn": 78}, {"ruleId": "50", "severity": 1, "message": "51", "line": 72, "column": 13, "nodeType": "52", "endLine": 72, "endColumn": 78}, {"ruleId": "50", "severity": 1, "message": "51", "line": 77, "column": 13, "nodeType": "52", "endLine": 77, "endColumn": 76}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement"]